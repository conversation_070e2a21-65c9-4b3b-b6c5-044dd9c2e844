// Image and video preview functionality
document.addEventListener('DOMContentLoaded', function() {
  const fileInput = document.getElementById('file');
  const previewContainer = document.getElementById('preview-container');
  const imagePreview = document.getElementById('image-preview');
  const videoPreview = document.getElementById('video-preview');
  const uploadForm = document.getElementById('upload-form');
  const submitBtn = document.getElementById('submit-btn');
  const loadingOverlay = document.getElementById('loading-overlay');
  
  if (fileInput && (imagePreview || videoPreview)) {
    fileInput.addEventListener('change', function() {
      if (fileInput.files && fileInput.files[0]) {
        const file = fileInput.files[0];
        const fileType = file.type;
        
        // Tampilkan container preview
        previewContainer.style.display = 'block';
        
        if (fileType.startsWith('image/')) {
          // Jika file adalah gambar
          const reader = new FileReader();
          
          reader.onload = function(e) {
            imagePreview.src = e.target.result;
            imagePreview.style.display = 'block';
            if (videoPreview) videoPreview.style.display = 'none';
          };
          
          reader.readAsDataURL(file);
        } else if (fileType.startsWith('video/')) {
          // Jika file adalah video
          const url = URL.createObjectURL(file);
          videoPreview.src = url;
          videoPreview.style.display = 'block';
          if (imagePreview) imagePreview.style.display = 'none';
          
          // Bersihkan URL objek saat video dimuat
          videoPreview.onloadeddata = function() {
            URL.revokeObjectURL(url);
          };
        }
      }
    });
  }
  
  // Tampilkan loading overlay saat form disubmit
  if (uploadForm && loadingOverlay) {
    uploadForm.addEventListener('submit', function() {
      loadingOverlay.style.display = 'flex';
      if (submitBtn) {
        submitBtn.disabled = true;
      }
    });
  }
});