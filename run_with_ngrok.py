di bimport uvicorn
from pyngrok import ngrok
import os
import torch
from basicsr.archs.rrdbnet_arch import RRDBNet
from realesrgan import RealESRGANer
from app import app

# Ensure model directories exist
os.makedirs('weights', exist_ok=True)

# Konfigurasi port
port = 8000

# Pre-download and verify models before starting server
print("Initializing RealESRGAN models...")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Define model architectures correctly
models = {
    'realesrgan-x4plus': {
        'name': 'RealESRGAN x4plus',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    },
    'realesrgan-x2plus': {
        'name': 'RealESRGAN x2plus',
        'scale': 2,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=2)
    }
}

# Test load the default model to verify it works
try:
    default_model = 'realesrgan-x4plus'
    model_info = models[default_model]
    model = model_info['model']()
    
    # Initialize RealESRGANer
    upsampler = RealESRGANer(
        scale=model_info['scale'],
        model_path=model_info['model_path'],
        model=model,
        tile=512,
        tile_pad=16,
        pre_pad=10,
        half=(device == 'cuda'),
        device=device
    )
    print(f"Successfully loaded {default_model} model")
except Exception as e:
    print(f"Warning: Error loading model: {e}")
    print("The application will continue but may encounter errors when processing images.")

# Buat tunnel ngrok
tunnel = ngrok.connect(port, "http")
public_url = tunnel.public_url
print(f"Aplikasi dapat diakses melalui: {public_url}")

# Pastikan FastAPI tahu URL publik
os.environ["BASE_URL"] = public_url

# Jalankan aplikasi
if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=port)