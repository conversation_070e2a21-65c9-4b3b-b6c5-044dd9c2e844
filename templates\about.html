{% extends 'base.html' %}

{% block content %}
<div class="about-container">
    <div class="about-header">
        <h2>Tentang Billy'S Upscaler</h2>
        <p class="about-subtitle">Platform AI untuk meningkatkan kualitas gambar dan video</p>
    </div>

    <div class="about-content">
        <section class="about-section">
            <h3>Apa itu Billy'S Upscaler?</h3>
            <p>
                Billy'S Upscaler adalah aplikasi web yang menggunakan teknologi kecerdasan buatan (AI) untuk 
                meningkatkan kualitas gambar dan video. Dengan menggunakan algoritma deep learning yang canggih, 
                aplikasi ini dapat memperbesar resolusi gambar hingga 4x lipat sambil mempertahankan dan bahkan 
                meningkatkan detail visual.
            </p>
        </section>

        <section class="about-section">
            <h3>Fitur Utama</h3>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                        </svg>
                    </div>
                    <h4>Multiple AI Models</h4>
                    <p>Berbagai model AI yang dioptimalkan untuk jenis gambar yang berbeda (foto, anime, video)</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83"></path>
                        </svg>
                    </div>
                    <h4>Upscaling 2x-4x</h4>
                    <p>Tingkatkan resolusi gambar hingga 4x lipat dengan kualitas yang superior</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                        </svg>
                    </div>
                    <h4>Format Beragam</h4>
                    <p>Mendukung berbagai format gambar (PNG, JPG, WEBP) dan video (MP4, AVI, MOV, MKV)</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
                        </svg>
                    </div>
                    <h4>Interface Sederhana</h4>
                    <p>Antarmuka yang mudah digunakan, cukup upload dan dapatkan hasil berkualitas tinggi</p>
                </div>
            </div>
        </section>

        <section class="about-section">
            <h3>Teknologi yang Digunakan</h3>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>Real-ESRGAN</h4>
                    <p>
                        Algoritma AI state-of-the-art yang dikembangkan oleh Xintao Wang dan tim dari Tencent ARC Lab. 
                        Real-ESRGAN menggunakan Generative Adversarial Networks (GAN) untuk menghasilkan gambar 
                        berkualitas tinggi dengan detail yang realistis.
                    </p>
                </div>
                <div class="tech-item">
                    <h4>PyTorch</h4>
                    <p>Framework deep learning yang digunakan untuk menjalankan model AI dengan performa optimal.</p>
                </div>
                <div class="tech-item">
                    <h4>FastAPI</h4>
                    <p>Framework web modern dan cepat untuk Python yang menyediakan API backend yang responsif.</p>
                </div>
                <div class="tech-item">
                    <h4>OpenCV</h4>
                    <p>Library computer vision untuk pemrosesan gambar dan video yang efisien.</p>
                </div>
            </div>
        </section>

        <section class="about-section credits-section">
            <h3>Credits & Acknowledgments</h3>
            <div class="credits-content">
                <div class="credit-item">
                    <h4>Real-ESRGAN</h4>
                    <p>
                        Terima kasih kepada <strong>Xintao Wang</strong> dan tim dari <strong>Tencent ARC Lab</strong> 
                        yang telah mengembangkan algoritma Real-ESRGAN yang luar biasa ini.
                    </p>
                    <div class="credit-links">
                        <a href="https://github.com/xinntao/Real-ESRGAN" target="_blank" class="credit-link">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                            </svg>
                            GitHub Repository
                        </a>
                        <a href="https://arxiv.org/abs/2107.10833" target="_blank" class="credit-link">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14 2 14 8 20 8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10 9 9 9 8 9"></polyline>
                            </svg>
                            Research Paper
                        </a>
                    </div>
                </div>
                
                <div class="credit-item">
                    <h4>Citation</h4>
                    <div class="citation-box">
                        <pre>@InProceedings{wang2021realesrgan,
    author    = {Xintao Wang and Liangbin Xie and Chao Dong and Ying Shan},
    title     = {Real-ESRGAN: Training Real-World Blind Super-Resolution with Pure Synthetic Data},
    booktitle = {International Conference on Computer Vision (ICCV)},
    date      = {2021}
}</pre>
                    </div>
                </div>

                <div class="credit-item">
                    <h4>Developer</h4>
                    <p>
                        Billy'S Upscaler dikembangkan sebagai implementasi web-based dari teknologi Real-ESRGAN 
                        untuk memudahkan akses dan penggunaan oleh pengguna umum.
                    </p>
                </div>
            </div>
        </section>

        <section class="about-section">
            <h3>Cara Menggunakan</h3>
            <div class="usage-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Upload File</h4>
                        <p>Pilih gambar atau video yang ingin ditingkatkan kualitasnya</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Pilih Model</h4>
                        <p>Pilih model AI yang sesuai dengan jenis konten Anda</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Proses</h4>
                        <p>Klik tombol upscale dan tunggu proses selesai</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Download</h4>
                        <p>Download hasil gambar atau video yang telah ditingkatkan</p>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
{% endblock %}
