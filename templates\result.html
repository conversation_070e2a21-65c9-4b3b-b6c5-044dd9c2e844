{% extends 'base.html' %}

{% block content %}
<div class="result-container">
    <h2>Hasil Upscaling</h2>

    <div class="model-info">
        <p>Model yang digunakan: <strong>{{ model_used }}</strong></p>
    </div>

    <div class="comparison-wrapper">
        <div class="image-comparison">
            <div class="image-section">
                <h3>Gambar Asli</h3>
                <div class="image-container">
                    <img src="{{ input_image }}" alt="Gambar Asli" class="result-image">
                </div>
            </div>

            <div class="image-section">
                <h3>Hasil Upscale</h3>
                <div class="image-container">
                    <img src="{{ output_image }}" alt="Gambar Hasil Upscale" class="result-image">
                </div>
            </div>
        </div>
    </div>
    
    <div class="download-container">
        <a href="{{ download_url }}" class="download-button" download>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            <span>Download Hasil</span>
        </a>
    </div>
    
    <div class="action-buttons">
        <a href="/" class="back-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
            <span>Upscale {% if is_video %}Video{% else %}Gambar{% endif %} Lain</span>
        </a>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .result-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .result-container h2 {
        text-align: center;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .model-info {
        text-align: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background-color: rgba(255, 0, 0, 0.05);
        border-radius: 8px;
        border: 1px solid rgba(255, 0, 0, 0.1);
    }

    .model-info p {
        margin: 0;
        font-size: 1.1rem;
    }

    .model-info strong {
        color: var(--primary-color);
    }

    .comparison-wrapper {
        margin: 2rem 0;
    }

    .image-comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .image-section {
        background-color: var(--card-bg);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
    }

    .image-section h3 {
        text-align: center;
        margin-bottom: 1rem;
        color: var(--text-color);
        font-size: 1.2rem;
    }

    .image-container {
        position: relative;
        width: 100%;
        min-height: 300px;
        background-color: rgba(255, 255, 255, 0.02);
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .result-image {
        max-width: 100%;
        max-height: 400px;
        width: auto;
        height: auto;
        object-fit: contain;
        border-radius: 6px;
    }

    .download-container {
        text-align: center;
        margin: 2rem 0;
    }

    .download-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background-color: var(--primary-color);
        color: white;
        text-decoration: none;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .download-button:hover {
        background-color: var(--primary-hover);
        transform: translateY(-2px);
    }

    .action-buttons {
        text-align: center;
        margin-top: 2rem;
    }

    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background-color: var(--card-bg);
        color: var(--text-color);
        text-decoration: none;
        padding: 0.8rem 1.5rem;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .back-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .image-comparison {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .result-container {
            padding: 1rem;
        }

        .image-section {
            padding: 1rem;
        }

        .result-image {
            max-height: 250px;
        }
    }
</style>
{% endblock %}
