/* Loading Animation Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 6px solid rgba(255, 0, 0, 0.3);
    border-radius: 50%;
    border-top: 6px solid var(--primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: white;
    font-size: 1.2rem;
    text-align: center;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.loading-text {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
}

.progress-container {
    width: 300px;
    height: 10px;
    background-color: #333;
    border-radius: 5px;
    margin-top: 20px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    width: 0%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

/* Pulse animation for loading text */
@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.loading-text {
    animation: pulse 1.5s infinite;
}