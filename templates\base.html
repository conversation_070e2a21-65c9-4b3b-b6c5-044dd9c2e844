<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BillUpscaler</title>
    <link rel="stylesheet" href="{{ url_for('static', path='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/loading.css') }}">
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', path='favicon.svg') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% block styles %}{% endblock %}
</head>
<body>
    <div class="container">
        <header>
            <h1>Billy'S Upscaler</h1>
            <p class="tagline">Tingkatkan kualitas gambar dengan AI</p>

            <hr>
        </header>
        
        <main>
            {% block content %}{% endblock %}
        </main>
        
        <!-- Real-ESRGAN Information Section -->
        <section class="realesrgan-info">
            <div class="info-container">
                <h3>Tentang Real-ESRGAN</h3>
                <p class="info-description">
                    Billy'S Upscaler menggunakan teknologi <strong>Real-ESRGAN</strong>, sebuah algoritma AI canggih
                    yang dikembangkan untuk meningkatkan kualitas gambar dengan hasil yang sangat realistis.
                    Real-ESRGAN menggunakan Generative Adversarial Networks (GAN) untuk menghasilkan detail
                    yang tajam dan natural pada gambar yang diperbesar.
                </p>

                <div class="credits-section">
                    <h4>Credits & Acknowledgments</h4>
                    <div class="credit-content">
                        <p>
                            Terima kasih kepada <strong>Xintao Wang</strong> dan tim dari <strong>Tencent ARC Lab</strong>
                            yang telah mengembangkan algoritma Real-ESRGAN yang luar biasa ini.
                        </p>

                        <div class="credit-links">
                            <a href="https://github.com/xinntao/Real-ESRGAN" target="_blank" class="credit-link">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                </svg>
                                GitHub Repository
                            </a>
                            <a href="https://arxiv.org/abs/2107.10833" target="_blank" class="credit-link">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10 9 9 9 8 9"></polyline>
                                </svg>
                                Research Paper
                            </a>
                        </div>

                        <div class="citation-box">
                            <h5>Citation</h5>
                            <pre>@InProceedings{wang2021realesrgan,
    author    = {Xintao Wang and Liangbin Xie and Chao Dong and Ying Shan},
    title     = {Real-ESRGAN: Training Real-World Blind Super-Resolution with Pure Synthetic Data},
    booktitle = {International Conference on Computer Vision (ICCV)},
    date      = {2021}
}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <footer>
            <p>Billy'S Upscaler - Powered by <a href="https://github.com/xinntao/Real-ESRGAN" target="_blank">Real-ESRGAN</a></p>
        </footer>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p>Memproses gambar... Mohon tunggu.</p>
    </div>



    <script src="{{ url_for('static', path='js/preview.js') }}"></script>
</script>
</script>
</script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const modelSelect = document.getElementById('model-select');
            const modelDescription = document.getElementById('model-description');
            
            if (modelSelect && modelDescription) {
                modelSelect.addEventListener('change', function() {
                    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
                    const description = selectedOption.text.split(' - ')[1];
                    modelDescription.textContent = description || '';
                });
            }
        });
    </script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>
    


